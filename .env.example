# AI API Configuration (Use either <PERSON><PERSON><PERSON> or Gemini - bot will auto-detect which is available)
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.3

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-pro-preview-05-06
GEMINI_MAX_TOKENS=1000
GEMINI_TEMPERATURE=0.3

# Deriv API Configuration (REQUIRED for binary options)
DERIV_API_TOKEN=your_deriv_api_token_here
DERIV_APP_ID=1089

# Binary Options Trading Configuration
DEFAULT_STAKE=1.0
DEFAULT_DURATION=5
DEFAULT_DURATION_UNIT=t
MAX_STAKE_PER_TRADE=10.0
MAX_DAILY_LOSS=100.0
MAX_CONCURRENT_TRADES=3

# Binary Options Symbols (comma-separated)
BINARY_OPTIONS_SYMBOLS=R_10,R_25,R_50,EURUSD,GBPUSD

# Analysis Configuration
ENABLE_REAL_TIME_ANALYSIS=true
ANALYSIS_INTERVAL_SECONDS=30
TICK_HISTORY_COUNT=100
CANDLE_HISTORY_COUNT=50

# Logging Configuration
LOG_LEVEL=INFO

# =============================================================================
# SETUP INSTRUCTIONS FOR BINARY OPTIONS TRADING
# =============================================================================
# 1. Get your Deriv API token from: https://app.deriv.com/account/api-token
# 2. Get an AI API key (choose one):
#    - OpenAI API key from: https://platform.openai.com/api-keys
#    - Google Gemini API key from: https://aistudio.google.com/app/apikey
# 3. Copy this file to .env and replace the placeholder values
# 4. Run the binary options bot with: python binary_options_bot.py
#
# AI MODEL SELECTION:
# - The bot will automatically detect which AI API key is available
# - If both are provided, Gemini will be preferred for better performance
# - Gemini 2.5 Pro Preview offers enhanced reasoning and analysis capabilities
#
# DERIV API FEATURES:
# - Real-time binary options trading
# - Synthetic indices (R_10, R_25, R_50, R_75, R_100, etc.)
# - Forex pairs (EURUSD, GBPUSD, etc.)
# - WebSocket-based real-time data
# - Demo and real money accounts available
#
# BINARY OPTIONS SYMBOLS:
# - R_10, R_25, R_50, R_75, R_100: Volatility indices
# - EURUSD, GBPUSD, USDJPY: Major forex pairs for binary options
# - Duration: 1-10 ticks (t) or 1-10 minutes (m)
#
# RISK MANAGEMENT:
# - DEFAULT_STAKE: Amount per trade (start small!)
# - MAX_DAILY_LOSS: Stop trading when this loss is reached
# - MAX_CONCURRENT_TRADES: Maximum open positions at once
